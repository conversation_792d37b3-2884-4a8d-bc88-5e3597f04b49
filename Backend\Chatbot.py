import os
import json
import uuid
import requests
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1/chat/completions"
MODEL_NAME = "deepseek/deepseek-chat-v3:free"
CHAT_HISTORY_FILE = "Backend/Chat.json"

class ChatBot:
    def __init__(self):
        self.session_id = str(uuid.uuid4())
        self.initialize_chat_history()

    def initialize_chat_history(self):
        """Initialize or clear chat history"""
        chat_data = {
            "conversations": [],
            "session_id": self.session_id,
            "created_at": datetime.now().isoformat()
        }

        # Ensure Backend directory exists
        os.makedirs('Backend', exist_ok=True)

        with open(CHAT_HISTORY_FILE, 'w') as f:
            json.dump(chat_data, f, indent=2)

    def load_chat_history(self):
        """Load chat history from JSON file"""
        try:
            with open(CHAT_HISTORY_FILE, 'r') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.initialize_chat_history()
            return self.load_chat_history()

    def save_message_to_history(self, user_message, bot_response):
        """Save conversation to chat history"""
        chat_data = self.load_chat_history()

        conversation_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_message": user_message,
            "bot_response": bot_response,
            "message_id": str(uuid.uuid4())
        }

        chat_data["conversations"].append(conversation_entry)

        with open(CHAT_HISTORY_FILE, 'w') as f:
            json.dump(chat_data, f, indent=2)

    def get_openrouter_response(self, user_message):
        """Get response from OpenRouter API using DeepSeek Chat v3 model"""
        # Prepare the conversation history for context
        chat_data = self.load_chat_history()
        messages = []

        # Add previous conversations for context (last 10 messages to avoid token limits)
        recent_conversations = chat_data["conversations"][-5:] if chat_data["conversations"] else []

        for conv in recent_conversations:
            messages.append({"role": "user", "content": conv["user_message"]})
            messages.append({"role": "assistant", "content": conv["bot_response"]})

        # Add current user message
        messages.append({"role": "user", "content": user_message})

        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": MODEL_NAME,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 1500,
            "stream": False
        }

        try:
            response = requests.post(OPENROUTER_BASE_URL, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            response_data = response.json()

            if "choices" in response_data and len(response_data["choices"]) > 0:
                choice = response_data["choices"][0]
                bot_message = choice["message"]["content"]

                return {
                    "success": True,
                    "response": bot_message
                }
            else:
                return {
                    "success": False,
                    "response": "Sorry, I couldn't generate a response. Please try again."
                }

        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "response": f"Connection error: {str(e)}"
            }
        except json.JSONDecodeError as e:
            return {
                "success": False,
                "response": f"Invalid response format: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "response": f"Unexpected error: {str(e)}"
            }

# Initialize ChatBot instance
chatbot = ChatBot()

@app.route('/')
def home():
    """Health check endpoint"""
    return jsonify({
        "status": "ChatBot API is running",
        "session_id": chatbot.session_id,
        "model": MODEL_NAME,
        "api_key_configured": bool(OPENROUTER_API_KEY)
    })

@app.route('/chat', methods=['POST'])
def chat():
    """Main chat endpoint"""
    try:
        data = request.get_json()

        if not data or 'message' not in data:
            return jsonify({
                "success": False,
                "response": "Please provide a message to chat."
            }), 400

        user_message = data['message'].strip()

        if not user_message:
            return jsonify({
                "success": False,
                "response": "Please provide a non-empty message."
            }), 400

        # Get response from OpenRouter
        ai_response = chatbot.get_openrouter_response(user_message)

        # Save to history if successful
        if ai_response["success"]:
            chatbot.save_message_to_history(user_message, ai_response["response"])

        return jsonify({
            "success": ai_response["success"],
            "user_message": user_message,
            "response": ai_response["response"],
            "session_id": chatbot.session_id,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "response": f"Server error: {str(e)}"
        }), 500

@app.route('/clear', methods=['POST'])
def clear_history():
    """Clear chat history (simulates page refresh)"""
    try:
        chatbot.initialize_chat_history()
        return jsonify({
            "success": True,
            "message": "Chat history cleared successfully",
            "session_id": chatbot.session_id
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Failed to clear history: {str(e)}"
        }), 500

if __name__ == '__main__':
    # Ensure the Backend directory exists
    os.makedirs('Backend', exist_ok=True)

    # Initialize chat history on startup
    chatbot.initialize_chat_history()

    print(f"🤖 ChatBot API starting...")
    print(f"📝 Session ID: {chatbot.session_id}")
    print(f"🔑 API Key configured: {'Yes' if OPENROUTER_API_KEY else 'No'}")
    print(f"🎯 Model: {MODEL_NAME}")
    print(f"📁 Chat history: {CHAT_HISTORY_FILE}")
    print(f"🌐 Server running on: http://localhost:5000")

    app.run(debug=True, host='0.0.0.0', port=5000)