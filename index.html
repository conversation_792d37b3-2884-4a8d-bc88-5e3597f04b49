<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBot - Simple Q&A Interface</title>
    <link rel="stylesheet" href="Css/style.css">
    <link rel="stylesheet" href="Css/animations.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 ChatBot</h1>
            <p>Ask me anything!</p>
        </header>

        <main>
            <div class="chat-interface">
                <div class="input-section">
                    <label for="questionInput">Enter Your Question:</label>
                    <div class="input-group">
                        <textarea
                            id="questionInput"
                            placeholder="Type your question here..."
                            rows="3"
                        ></textarea>
                        <div class="button-group">
                            <button id="sendButton" class="send-btn">Send</button>
                            <button id="clearButton" class="clear-btn">Clear History</button>
                        </div>
                    </div>
                </div>

                <div class="response-section">
                    <div id="responseArea" class="response-area">
                        <div class="response info">
                            <div class="response-content">
                                <div class="response-text">
                                    👋 Welcome! I'm ready to answer your questions.
                                    <br><br>
                                    📝 Type your question above and click "Send" or press Enter.
                                    <br>
                                    🔄 Use "Clear History" to reset our conversation.
                                </div>
                                <div class="response-time">Ready</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>Powered by OpenRouter.ai & DeepSeek Chat v3</p>
        </footer>
    </div>

    <script src="Backend/Data_Reader.js"></script>
</body>
</html>