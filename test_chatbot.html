<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBot Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>ChatBot JavaScript Test</h1>
    <div id="test-results"></div>
    
    <script src="Backend/Data_Reader.js"></script>
    <script>
        // Test the ChatBot functionality
        async function runTests() {
            const results = document.getElementById('test-results');
            
            function addResult(message, type) {
                const div = document.createElement('div');
                div.className = `test-result ${type}`;
                div.textContent = message;
                results.appendChild(div);
            }
            
            addResult('Starting ChatBot tests...', 'info');
            
            try {
                // Test 1: Initialize ChatBot
                const chatBot = new ChatBot();
                addResult('✅ ChatBot initialized successfully', 'success');
                
                // Test 2: Check API status
                const status = chatBot.checkApiStatus();
                addResult(`✅ API Status: ${status.status}`, 'success');
                addResult(`📝 Session ID: ${status.session_id}`, 'info');
                addResult(`🎯 Model: ${status.model}`, 'info');
                addResult(`🔑 API Key: ${status.api_key_configured ? 'Configured' : 'Not configured'}`, status.api_key_configured ? 'success' : 'error');
                
                // Test 3: Test history functions
                chatBot.clearHistoryOnLoad();
                addResult('✅ History cleared successfully', 'success');
                
                // Test 4: Test API call (this will make a real API request)
                addResult('🔄 Testing API call...', 'info');
                const testResponse = await chatBot.callOpenRouterAPI('Hello, this is a test message');
                
                if (testResponse.success) {
                    addResult('✅ API call successful!', 'success');
                    addResult(`🤖 Response: ${testResponse.response.substring(0, 100)}...`, 'info');
                } else {
                    addResult(`❌ API call failed: ${testResponse.response}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Test failed: ${error.message}`, 'error');
            }
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runTests, 1000); // Wait a bit for everything to load
        });
    </script>
</body>
</html>
