/* ChatBot Animations */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes typing {
    0%, 20% {
        content: "🤔 Thinking";
    }
    25%, 45% {
        content: "🤔 Thinking.";
    }
    50%, 70% {
        content: "🤔 Thinking..";
    }
    75%, 95% {
        content: "🤔 Thinking...";
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Animation Classes */
.fade-in-up {
    animation: fadeInUp 0.5s ease;
}

.pulse {
    animation: pulse 2s infinite;
}

.slide-in {
    animation: slideIn 0.5s ease;
}

.bounce {
    animation: bounce 1s;
}

/* Loading Animation for Buttons */
.send-btn.loading {
    position: relative;
    color: transparent;
}

.send-btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border-radius: 50%;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Typing Indicator */
.typing-indicator {
    display: inline-block;
    position: relative;
}

.typing-indicator::after {
    content: "🤔 Thinking";
    animation: typing 2s infinite;
}

/* Hover Effects */
.chat-interface {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chat-interface:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

/* Response Animation Variants */
.response.success {
    animation: fadeInUp 0.5s ease, pulse 0.3s ease 0.2s;
}

.response.error {
    animation: fadeInUp 0.5s ease, bounce 0.6s ease 0.2s;
}

.response.loading {
    animation: fadeInUp 0.5s ease;
}

.response.loading .response-text {
    position: relative;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Input Focus Animation */
#questionInput:focus {
    animation: pulse 0.3s ease;
}

/* Button Press Animation */
button:active {
    transform: scale(0.98);
}

/* Smooth Transitions */
* {
    transition: all 0.3s ease;
}

/* Disable transitions for reduced motion users */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}