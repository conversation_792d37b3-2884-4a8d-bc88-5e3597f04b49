/* ChatGPT Interface Animations */

/* Message Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Typing Indicator Animation */
@keyframes typingDot {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    40% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Button Hover Animation */
@keyframes buttonHover {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Input Focus Animation */
@keyframes inputFocus {
    0% {
        border-color: #424242;
    }
    100% {
        border-color: #565656;
    }
}

/* Message Entry Animations */
.message-group {
    animation: fadeInUp 0.3s ease-out;
}

.user-message {
    animation: slideInFromRight 0.3s ease-out;
}

.assistant-message {
    animation: fadeInUp 0.4s ease-out;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 0;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #8e8ea0;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
    animation-delay: -0.16s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0s;
}

/* Button Animations */
.send-button {
    transition: all 0.2s ease;
}

.send-button:hover:not(:disabled) {
    animation: buttonHover 0.3s ease;
}

.send-button:active {
    transform: scale(0.95);
}

/* Input Animations */
.message-input {
    transition: all 0.2s ease;
}

.input-wrapper:focus-within {
    animation: inputFocus 0.2s ease;
}

/* Smooth Scrolling */
.messages-area {
    scroll-behavior: smooth;
}

/* Avatar Animations */
.avatar-icon {
    transition: all 0.2s ease;
}

.message:hover .avatar-icon {
    transform: scale(1.1);
}

/* Text Animation for Long Messages */
.message-text {
    animation: fadeInUp 0.5s ease-out;
}

/* Header Animation */
.header-title {
    transition: all 0.3s ease;
}

/* Responsive Animation Adjustments */
@media (max-width: 768px) {
    .message-group {
        animation-duration: 0.2s;
    }

    .typing-indicator {
        padding: 6px 0;
    }

    .typing-dot {
        width: 6px;
        height: 6px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .typing-dot {
        animation: none;
        opacity: 0.6;
    }
}

/* Focus Animations */
.message-input:focus {
    outline: none;
}

/* Loading State Animations */
.send-button:disabled {
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

/* Message Hover Effects */
.message:hover {
    background-color: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

/* Smooth Transitions for All Interactive Elements */
button, input, textarea {
    transition: all 0.2s ease;
}

/* Welcome Message Animation */
.message-group:first-child {
    animation: fadeInUp 0.6s ease-out;
}