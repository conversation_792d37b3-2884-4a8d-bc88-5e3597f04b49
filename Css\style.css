/* ChatBot Interface Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-interface {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    padding: 30px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.input-section label {
    display: block;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #4a5568;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

#questionInput {
    width: 100%;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    transition: border-color 0.3s ease;
}

#questionInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#questionInput:disabled {
    background-color: #f7fafc;
    cursor: not-allowed;
}

.button-group {
    display: flex;
    gap: 15px;
    justify-content: flex-start;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.send-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.send-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.clear-btn {
    background: #f56565;
    color: white;
}

.clear-btn:hover {
    background: #e53e3e;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(245, 101, 101, 0.4);
}

.response-section {
    flex: 1;
    min-height: 200px;
}

.response-area {
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
    min-height: 200px;
    border: 2px solid #e2e8f0;
}

.response {
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    animation: fadeInUp 0.5s ease;
}

.response.success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.response.error {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
}

.response.loading {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
}

.response.info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.response-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.response-text {
    font-size: 1rem;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.response-time {
    font-size: 0.85rem;
    opacity: 0.8;
    text-align: right;
    font-style: italic;
}

footer {
    text-align: center;
    margin-top: 20px;
    color: white;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    header h1 {
        font-size: 2rem;
    }

    .chat-interface {
        padding: 20px;
    }

    .button-group {
        flex-direction: column;
    }

    button {
        width: 100%;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.8rem;
    }

    .chat-interface {
        padding: 15px;
    }

    #questionInput {
        min-height: 60px;
    }
}