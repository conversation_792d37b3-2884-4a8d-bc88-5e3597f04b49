/* ChatGPT Interface Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #212121;
    color: #ececec;
    height: 100vh;
    overflow: hidden;
}

.app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    background-color: #212121;
    border-bottom: 1px solid #424242;
    padding: 0 16px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 768px;
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    color: #ececec;
    text-align: center;
    width: 100%;
}

/* Main Chat Area */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 768px;
    margin: 0 auto;
    width: 100%;
}

/* Messages Area */
.messages-area {
    flex: 1;
    overflow-y: auto;
    padding: 24px 16px 0;
    scroll-behavior: smooth;
}

.message-group {
    margin-bottom: 24px;
}

.message {
    display: flex;
    gap: 16px;
    max-width: 100%;
}

.message-avatar {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #10a37f;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.user-message .avatar-icon {
    background-color: #ab68ff;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-text {
    font-size: 16px;
    line-height: 1.5;
    color: #ececec;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.user-message {
    margin-bottom: 24px;
}

.user-message .message-text {
    background-color: #2f2f2f;
    padding: 12px 16px;
    border-radius: 18px;
    display: inline-block;
    max-width: fit-content;
}

.assistant-message .message-text {
    padding: 0;
}

/* Input Area */
.input-area {
    padding: 16px;
    background-color: #212121;
    border-top: 1px solid #424242;
}

.input-container {
    max-width: 768px;
    margin: 0 auto;
}

.input-wrapper {
    position: relative;
    background-color: #2f2f2f;
    border-radius: 24px;
    border: 1px solid #424242;
    display: flex;
    align-items: flex-end;
    padding: 12px 16px;
    transition: border-color 0.2s ease;
}

.input-wrapper:focus-within {
    border-color: #565656;
}

.message-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: #ececec;
    font-size: 16px;
    line-height: 1.5;
    resize: none;
    max-height: 200px;
    min-height: 24px;
    font-family: inherit;
    padding: 0;
}

.message-input::placeholder {
    color: #8e8ea0;
}

.send-button {
    background-color: #ececec;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-left: 8px;
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    background-color: #d1d5db;
}

.send-button:disabled {
    background-color: #565656;
    cursor: not-allowed;
}

.send-button svg {
    color: #212121;
}

.send-button:disabled svg {
    color: #8e8ea0;
}

.input-footer {
    text-align: center;
    margin-top: 8px;
}

.input-disclaimer {
    font-size: 12px;
    color: #8e8ea0;
}

/* Hidden Clear Button */
.hidden-clear-btn {
    display: none;
}

/* Loading Animation */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 16px 0;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #8e8ea0;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    40% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Scrollbar Styling */
.messages-area::-webkit-scrollbar {
    width: 6px;
}

.messages-area::-webkit-scrollbar-track {
    background: transparent;
}

.messages-area::-webkit-scrollbar-thumb {
    background-color: #424242;
    border-radius: 3px;
}

.messages-area::-webkit-scrollbar-thumb:hover {
    background-color: #565656;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 0 12px;
    }

    .messages-area {
        padding: 16px 12px 0;
    }

    .input-area {
        padding: 12px;
    }

    .message {
        gap: 12px;
    }

    .message-text {
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .header-title {
        font-size: 16px;
    }

    .message-text {
        font-size: 14px;
    }

    .input-wrapper {
        padding: 10px 14px;
    }
}