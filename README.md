# ChatGPT Clone

A pixel-perfect ChatGPT clone that directly integrates with OpenRouter.ai using the DeepSeek Chat v3 model. No backend server required!

## Features

- 🎨 **Pixel-Perfect ChatGPT Interface**: Exact replica of ChatGPT's clean, modern design
- 🌟 **Welcome Screen Animation**: Centered input that smoothly transitions to chat mode
- 🎬 **Smooth Transitions**: Professional animations between welcome and chat states
- 🤖 **Direct OpenRouter.ai Integration**: Uses `deepseek/deepseek-chat-v3:free` model
- 💬 **Real-time Chat**: Conversation-style interface with user/assistant messages
- 🔄 **Typing Indicators**: Animated dots while AI is thinking
- 📜 **Scrollable Chat**: Proper page scrolling with fixed input bar
- 🌐 **Pure Frontend**: No backend server needed - runs entirely in browser
- 📝 **Context Aware**: Remembers conversation during session
- ⚡ **Instant Responses**: Direct API calls with smooth animations
- 🚀 **Zero Setup**: Just open HTML file and start chatting
- 📱 **Responsive**: Works perfectly on desktop and mobile
- 🎯 **First-Time Experience**: Engaging welcome screen that transitions to chat

## Setup Instructions

### 1. No Dependencies Required!
No Python installation or pip packages needed.

### 2. Open the Web Interface
Simply open `index.html` in your web browser - that's it!

### 3. Start Chatting
- Type your question in the "Enter Your Question:" field
- Click "Send" or press Enter
- Get instant AI responses

## Usage

### First-Time Experience
1. **Open the Interface**: Double-click `index.html` to open in your browser
2. **Welcome Screen**: You'll see a centered welcome screen with "Welcome to ChatGPT"
3. **Start Your First Chat**: Type your first message in the centered input field
4. **Watch the Transition**: The interface smoothly animates to full chat mode
5. **Continue Chatting**: Chat naturally - the AI remembers context

### Ongoing Usage
1. **Send Messages**: Type in the bottom input field and press Enter or click send
2. **View Responses**: See AI responses appear with typing indicators
3. **Scroll Through History**: Page scrolls naturally with fixed input at bottom
4. **Clear History**: Use browser refresh to return to welcome screen

## Interface Features

### Welcome Screen
- **Centered Layout**: Beautiful welcome message with centered input
- **Gradient Title**: "Welcome to ChatGPT" with gradient text effect
- **Smooth Focus**: Input field with glowing focus effects
- **Responsive Design**: Adapts perfectly to all screen sizes

### Chat Interface
- **Dark Theme**: Authentic ChatGPT dark mode design
- **Message Bubbles**: User messages in bubbles, AI responses in clean text
- **Avatar Icons**: User and assistant avatars for each message
- **Typing Indicators**: Animated dots while AI is generating responses
- **Fixed Input**: Input bar stays at bottom while scrolling
- **Smooth Animations**: Professional fade-in effects for new messages
- **Auto-scroll**: Page automatically scrolls to show new messages

## How It Works

The ChatBot uses direct API calls to OpenRouter.ai:
1. User types a question
2. JavaScript sends request directly to OpenRouter API
3. AI processes the request and returns response
4. Response is displayed instantly
5. Conversation history is stored in browser localStorage

## File Structure

```
ChatBot/
├── index.html             # Main web interface
├── Backend/
│   ├── Chatbot.py         # Python script (optional, for reference)
│   └── Data_Reader.js     # Frontend JavaScript with API integration
└── Css/
    ├── style.css          # Main styling
    └── animations.css     # Animations and effects
```

## Example Usage

### Simple Steps
1. Open `index.html` in any modern web browser
2. Type: "Hello, how are you?"
3. Click "Send" or press Enter
4. Get instant AI response
5. Continue conversation or clear history

### Example Conversation
```
You: Hello, how are you?
AI: Hello! I'm doing well, thank you for asking. I'm here and ready to help you with any questions or tasks you might have. How are you doing today?

You: What's 2+2?
AI: 2 + 2 = 4

You: Tell me a joke
AI: Why don't scientists trust atoms? Because they make up everything!
```

## Technical Details

- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **API**: Direct OpenRouter.ai integration
- **Model**: `deepseek/deepseek-chat-v3:free`
- **Context**: Keeps last 5 conversations for context
- **History**: Stored in browser localStorage
- **Session**: Each page load gets a new session ID
- **Auto-Clear**: History clears on page refresh
- **No Backend**: Runs entirely in the browser

## Browser Compatibility

Works in all modern browsers:
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## Security Note

The API key is embedded in the JavaScript for demo purposes. In production, you should:
- Use environment variables
- Implement proper API key management
- Add rate limiting
- Use HTTPS
