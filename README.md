# ChatBot Application

A web-based ChatBot with JavaScript frontend and Python Flask backend that integrates with OpenRouter.ai using the DeepSeek Chat v3 model.

## Features

- 🤖 **OpenRouter.ai Integration**: Uses `deepseek/deepseek-chat-v3:free` model
- 💬 **Chat History**: Stores conversations in `Chat.json` (clears on page refresh)
- 🌐 **Web Interface**: Clean HTML/CSS/JavaScript frontend
- 🔄 **Auto-Clear**: History automatically clears when page is refreshed
- 🔐 **Environment Variables**: API key stored securely in `.env` file
- 📝 **Context Aware**: Remembers conversation during session
- ⚡ **Real-time**: Instant responses with loading indicators

## Setup Instructions

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure API Key
The API key is already configured in the `.env` file. If you need to change it:
```
OPENROUTER_API_KEY=your-new-api-key-here
```

### 3. Start the Backend Server
```bash
python Backend/Chatbot.py
```

### 4. Open the Web Interface
Open `index.html` in your web browser or visit `http://localhost:5000` (if serving the HTML file)

## Usage

1. **Start the Backend**: Run `python Backend/Chatbot.py`
2. **Open Web Interface**: Open `index.html` in your browser
3. **Ask Questions**: Type in the "Enter Your Question:" field
4. **Get Responses**: Click "Send" or press Enter to get AI responses
5. **Clear History**: Click "Clear History" button to reset conversation
6. **Refresh**: Refreshing the page automatically clears chat history

## Interface

The web interface provides:
- **Clean Input Area**: Large text area for typing questions
- **Instant Responses**: Real-time AI responses with loading indicators
- **Visual Feedback**: Color-coded responses (success, error, loading, info)
- **Responsive Design**: Works on desktop and mobile devices
- **Smooth Animations**: Professional animations and transitions

## API Endpoints

The Flask backend provides these endpoints:

### 1. Health Check
- **GET** `/` - Check API status and configuration

### 2. Chat
- **POST** `/chat`
- **Body**: `{"message": "Your question here"}`
- **Response**: `{"success": true, "response": "AI response", ...}`

### 3. Clear History
- **POST** `/clear` - Clear chat history

## File Structure

```
ChatBot/
├── .env                    # Environment variables (API key)
├── requirements.txt        # Python dependencies
├── index.html             # Main web interface
├── Backend/
│   ├── Chatbot.py         # Flask API server
│   ├── Data_Reader.js     # Frontend JavaScript
│   └── Chat.json          # Chat history storage
└── Css/
    ├── style.css          # Main styling
    └── animations.css     # Animations and effects
```

## Example Usage

### Web Interface
1. Open `index.html` in browser
2. Type: "Hello, how are you?"
3. Click "Send" or press Enter
4. Get instant AI response
5. Continue conversation or clear history

### Direct API Usage
```bash
# Send a message
curl -X POST http://localhost:5000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?"}'

# Clear history
curl -X POST http://localhost:5000/clear
```

## Technical Details

- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Backend**: Python Flask with CORS support
- **Model**: `deepseek/deepseek-chat-v3:free`
- **API**: OpenRouter.ai
- **Context**: Keeps last 5 conversations for context
- **History**: Automatically saved to `Backend/Chat.json`
- **Session**: Each server restart gets a new session ID
- **Auto-Clear**: History clears on page refresh
