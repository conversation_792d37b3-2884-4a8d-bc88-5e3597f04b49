# ChatBot Application

A pure JavaScript ChatBot that directly integrates with OpenRouter.ai using the DeepSeek Chat v3 model. No backend server required!

## Features

- 🤖 **Direct OpenRouter.ai Integration**: Uses `deepseek/deepseek-chat-v3:free` model
- 💬 **Chat History**: Stores conversations in browser localStorage (clears on page refresh)
- 🌐 **Pure Frontend**: No backend server needed - runs entirely in browser
- 🔄 **Auto-Clear**: History automatically clears when page is refreshed
- 🔐 **Secure**: API key embedded in JavaScript (for demo purposes)
- 📝 **Context Aware**: Remembers conversation during session
- ⚡ **Instant**: Direct API calls with loading indicators
- 🚀 **Zero Setup**: Just open HTML file and start chatting

## Setup Instructions

### 1. No Dependencies Required!
No Python installation or pip packages needed.

### 2. Open the Web Interface
Simply open `index.html` in your web browser - that's it!

### 3. Start Chatting
- Type your question in the "Enter Your Question:" field
- Click "Send" or press Enter
- Get instant AI responses

## Usage

1. **Open the Interface**: Double-click `index.html` to open in your browser
2. **Ask Questions**: Type in the "Enter Your Question:" field
3. **Get Responses**: Click "Send" or press Enter to get AI responses
4. **Clear History**: Click "Clear History" button to reset conversation
5. **Refresh**: Refreshing the page automatically clears chat history

## Interface

The web interface provides:
- **Clean Input Area**: Large text area for typing questions
- **Instant Responses**: Real-time AI responses with loading indicators
- **Visual Feedback**: Color-coded responses (success, error, loading, info)
- **Responsive Design**: Works on desktop and mobile devices
- **Smooth Animations**: Professional animations and transitions

## How It Works

The ChatBot uses direct API calls to OpenRouter.ai:
1. User types a question
2. JavaScript sends request directly to OpenRouter API
3. AI processes the request and returns response
4. Response is displayed instantly
5. Conversation history is stored in browser localStorage

## File Structure

```
ChatBot/
├── index.html             # Main web interface
├── Backend/
│   ├── Chatbot.py         # Python script (optional, for reference)
│   └── Data_Reader.js     # Frontend JavaScript with API integration
└── Css/
    ├── style.css          # Main styling
    └── animations.css     # Animations and effects
```

## Example Usage

### Simple Steps
1. Open `index.html` in any modern web browser
2. Type: "Hello, how are you?"
3. Click "Send" or press Enter
4. Get instant AI response
5. Continue conversation or clear history

### Example Conversation
```
You: Hello, how are you?
AI: Hello! I'm doing well, thank you for asking. I'm here and ready to help you with any questions or tasks you might have. How are you doing today?

You: What's 2+2?
AI: 2 + 2 = 4

You: Tell me a joke
AI: Why don't scientists trust atoms? Because they make up everything!
```

## Technical Details

- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **API**: Direct OpenRouter.ai integration
- **Model**: `deepseek/deepseek-chat-v3:free`
- **Context**: Keeps last 5 conversations for context
- **History**: Stored in browser localStorage
- **Session**: Each page load gets a new session ID
- **Auto-Clear**: History clears on page refresh
- **No Backend**: Runs entirely in the browser

## Browser Compatibility

Works in all modern browsers:
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## Security Note

The API key is embedded in the JavaScript for demo purposes. In production, you should:
- Use environment variables
- Implement proper API key management
- Add rate limiting
- Use HTTPS
