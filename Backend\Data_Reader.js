// ChatBot Frontend JavaScript
// Handles user input and direct API communication

class ChatBot {
    constructor() {
        this.isLoading = false;
        this.apiKey = '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';
        this.apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
        this.model = 'deepseek/deepseek-chat-v3:free';
        this.chatHistory = [];
        this.sessionId = this.generateSessionId();
        this.initializeEventListeners();
        this.clearHistoryOnLoad();
    }

    generateSessionId() {
        return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    initializeEventListeners() {
        // Get DOM elements
        this.questionInput = document.getElementById('questionInput');
        this.sendButton = document.getElementById('sendButton');
        this.messagesArea = document.getElementById('messagesArea');
        this.clearButton = document.getElementById('clearButton');

        // Add event listeners
        if (this.sendButton) {
            this.sendButton.addEventListener('click', () => this.sendMessage());
        }

        if (this.questionInput) {
            // Auto-resize textarea
            this.questionInput.addEventListener('input', () => this.autoResizeTextarea());

            // Handle Enter key
            this.questionInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Enable/disable send button based on input
            this.questionInput.addEventListener('input', () => this.updateSendButton());
        }

        if (this.clearButton) {
            this.clearButton.addEventListener('click', () => this.clearHistory());
        }

        // Initial button state
        this.updateSendButton();
    }

    autoResizeTextarea() {
        const textarea = this.questionInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
    }

    updateSendButton() {
        const hasText = this.questionInput.value.trim().length > 0;
        this.sendButton.disabled = !hasText || this.isLoading;
    }

    clearHistoryOnLoad() {
        // Clear history when page loads (simulates refresh)
        this.chatHistory = [];
        this.saveHistoryToStorage();

        // Clear messages area except welcome message
        const welcomeMessage = this.messagesArea.querySelector('.message-group');
        this.messagesArea.innerHTML = '';
        if (welcomeMessage) {
            this.messagesArea.appendChild(welcomeMessage);
        }

        console.log('Chat history cleared on page load');
    }

    saveHistoryToStorage() {
        const historyData = {
            conversations: this.chatHistory,
            session_id: this.sessionId,
            created_at: new Date().toISOString()
        };
        localStorage.setItem('chatbot_history', JSON.stringify(historyData));
    }

    loadHistoryFromStorage() {
        try {
            const stored = localStorage.getItem('chatbot_history');
            if (stored) {
                const data = JSON.parse(stored);
                this.chatHistory = data.conversations || [];
                return data;
            }
        } catch (error) {
            console.error('Error loading history:', error);
        }
        return { conversations: [], session_id: this.sessionId, created_at: new Date().toISOString() };
    }

    async callOpenRouterAPI(userMessage) {
        try {
            // Prepare messages array with conversation history
            const messages = [];

            // Add recent conversation history (last 5 exchanges to avoid token limits)
            const recentHistory = this.chatHistory.slice(-5);
            for (const conv of recentHistory) {
                messages.push({ role: 'user', content: conv.user_message });
                messages.push({ role: 'assistant', content: conv.bot_response });
            }

            // Add current user message
            messages.push({ role: 'user', content: userMessage });

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': window.location.origin,
                    'X-Title': 'ChatBot Application'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: messages,
                    temperature: 0.7,
                    max_tokens: 1500,
                    stream: false
                })
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if (data.choices && data.choices.length > 0) {
                const botResponse = data.choices[0].message.content;

                // Save to history
                const conversationEntry = {
                    timestamp: new Date().toISOString(),
                    user_message: userMessage,
                    bot_response: botResponse,
                    message_id: this.generateMessageId()
                };

                this.chatHistory.push(conversationEntry);
                this.saveHistoryToStorage();

                return {
                    success: true,
                    user_message: userMessage,
                    response: botResponse,
                    session_id: this.sessionId,
                    timestamp: conversationEntry.timestamp,
                    usage: data.usage || {}
                };
            } else {
                throw new Error('No response from AI model');
            }

        } catch (error) {
            console.error('OpenRouter API Error:', error);
            return {
                success: false,
                response: `Error: ${error.message}`
            };
        }
    }

    generateMessageId() {
        return 'msg-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);
    }

    async sendMessage() {
        if (this.isLoading) return;

        const question = this.questionInput.value.trim();

        if (!question) {
            return;
        }

        // Add user message to chat
        this.addUserMessage(question);

        // Clear input and reset height
        this.questionInput.value = '';
        this.autoResizeTextarea();
        this.updateSendButton();

        // Show loading state
        this.setLoadingState(true);
        this.showTypingIndicator();

        try {
            // Call OpenRouter API directly
            const data = await this.callOpenRouterAPI(question);

            // Remove typing indicator
            this.removeTypingIndicator();

            if (data.success) {
                this.addAssistantMessage(data.response);
            } else {
                this.addAssistantMessage(`I apologize, but I encountered an error: ${data.response}`);
            }

        } catch (error) {
            this.removeTypingIndicator();
            this.addAssistantMessage(`I apologize, but I encountered an error: ${error.message}`);
        } finally {
            this.setLoadingState(false);
        }
    }

    addUserMessage(text) {
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group';

        messageGroup.innerHTML = `
            <div class="message user-message">
                <div class="message-avatar">
                    <div class="avatar-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" fill="currentColor"/>
                        </svg>
                    </div>
                </div>
                <div class="message-content">
                    <div class="message-text">${this.escapeHtml(text)}</div>
                </div>
            </div>
        `;

        this.messagesArea.appendChild(messageGroup);
        this.scrollToBottom();
    }

    addAssistantMessage(text) {
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group';

        messageGroup.innerHTML = `
            <div class="message assistant-message">
                <div class="message-avatar">
                    <div class="avatar-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
                        </svg>
                    </div>
                </div>
                <div class="message-content">
                    <div class="message-text">${this.escapeHtml(text)}</div>
                </div>
            </div>
        `;

        this.messagesArea.appendChild(messageGroup);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        const typingGroup = document.createElement('div');
        typingGroup.className = 'message-group typing-group';
        typingGroup.id = 'typing-indicator';

        typingGroup.innerHTML = `
            <div class="message assistant-message">
                <div class="message-avatar">
                    <div class="avatar-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
                        </svg>
                    </div>
                </div>
                <div class="message-content">
                    <div class="typing-indicator">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;

        this.messagesArea.appendChild(typingGroup);
        this.scrollToBottom();
    }

    removeTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    scrollToBottom() {
        this.messagesArea.scrollTop = this.messagesArea.scrollHeight;
    }

    async clearHistory() {
        try {
            this.chatHistory = [];
            this.saveHistoryToStorage();

            // Clear all messages except welcome message
            const welcomeMessage = this.messagesArea.querySelector('.message-group');
            this.messagesArea.innerHTML = '';
            if (welcomeMessage) {
                this.messagesArea.appendChild(welcomeMessage);
            }

            console.log('Chat history cleared manually');

        } catch (error) {
            console.error('Error clearing history:', error);
        }
    }

    setLoadingState(loading) {
        this.isLoading = loading;
        this.updateSendButton();

        if (this.questionInput) {
            this.questionInput.disabled = loading;
        }
    }

    // Utility method to check API status
    checkApiStatus() {
        const status = {
            status: "ChatBot is ready",
            session_id: this.sessionId,
            model: this.model,
            api_key_configured: !!this.apiKey,
            direct_api: true
        };
        console.log('ChatBot Status:', status);
        return status;
    }
}

// Initialize ChatBot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatBot = new ChatBot();

    // Check API status on load
    const status = window.chatBot.checkApiStatus();
    console.log('✅ ChatBot initialized with direct API access');
    console.log(`📝 Session ID: ${status.session_id}`);
    console.log(`🎯 Model: ${status.model}`);
    console.log(`🔑 API Key: ${status.api_key_configured ? 'Configured' : 'Not configured'}`);
    console.log('🌐 Using direct OpenRouter API calls');
});