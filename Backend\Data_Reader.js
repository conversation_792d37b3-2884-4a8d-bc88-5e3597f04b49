// ChatBot Frontend JavaScript
// Handles user input and communication with Python backend

class ChatBot {
    constructor() {
        this.apiUrl = 'http://localhost:5000';
        this.isLoading = false;
        this.initializeEventListeners();
        this.clearHistoryOnLoad();
    }

    initializeEventListeners() {
        // Get DOM elements
        this.questionInput = document.getElementById('questionInput');
        this.sendButton = document.getElementById('sendButton');
        this.responseArea = document.getElementById('responseArea');
        this.clearButton = document.getElementById('clearButton');

        // Add event listeners
        if (this.sendButton) {
            this.sendButton.addEventListener('click', () => this.sendMessage());
        }

        if (this.questionInput) {
            this.questionInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }

        if (this.clearButton) {
            this.clearButton.addEventListener('click', () => this.clearHistory());
        }
    }

    clearHistoryOnLoad() {
        // Clear history when page loads (simulates refresh)
        fetch(`${this.apiUrl}/clear`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        }).catch(error => {
            console.log('Note: Could not clear history on load:', error.message);
        });
    }

    async sendMessage() {
        if (this.isLoading) return;

        const question = this.questionInput.value.trim();

        if (!question) {
            this.showResponse('❌ Please enter a question.', 'error');
            return;
        }

        // Show loading state
        this.setLoadingState(true);
        this.showResponse('🤔 Thinking...', 'loading');

        try {
            const response = await fetch(`${this.apiUrl}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: question
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showResponse(data.response, 'success');
                this.questionInput.value = ''; // Clear input after successful response
            } else {
                this.showResponse(`❌ Error: ${data.response}`, 'error');
            }

        } catch (error) {
            this.showResponse(`❌ Connection Error: ${error.message}`, 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    async clearHistory() {
        try {
            const response = await fetch(`${this.apiUrl}/clear`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showResponse('🗑️ Chat history cleared!', 'info');
                this.questionInput.value = '';
            } else {
                this.showResponse(`❌ Error clearing history: ${data.message}`, 'error');
            }

        } catch (error) {
            this.showResponse(`❌ Error: ${error.message}`, 'error');
        }
    }

    showResponse(message, type = 'success') {
        if (!this.responseArea) return;

        // Create response element
        const responseElement = document.createElement('div');
        responseElement.className = `response ${type}`;

        // Add timestamp
        const timestamp = new Date().toLocaleTimeString();
        responseElement.innerHTML = `
            <div class="response-content">
                <div class="response-text">${message}</div>
                <div class="response-time">${timestamp}</div>
            </div>
        `;

        // Clear previous responses and add new one
        this.responseArea.innerHTML = '';
        this.responseArea.appendChild(responseElement);

        // Scroll to response
        responseElement.scrollIntoView({ behavior: 'smooth' });
    }

    setLoadingState(loading) {
        this.isLoading = loading;

        if (this.sendButton) {
            this.sendButton.disabled = loading;
            this.sendButton.textContent = loading ? 'Thinking...' : 'Send';
        }

        if (this.questionInput) {
            this.questionInput.disabled = loading;
        }
    }

    // Utility method to check API status
    async checkApiStatus() {
        try {
            const response = await fetch(`${this.apiUrl}/`);
            const data = await response.json();
            console.log('API Status:', data);
            return data;
        } catch (error) {
            console.error('API not available:', error);
            return null;
        }
    }
}

// Initialize ChatBot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatBot = new ChatBot();

    // Check API status on load
    window.chatBot.checkApiStatus().then(status => {
        if (status) {
            console.log('✅ ChatBot API is running');
            console.log(`📝 Session ID: ${status.session_id}`);
            console.log(`🎯 Model: ${status.model}`);
            console.log(`🔑 API Key: ${status.api_key_configured ? 'Configured' : 'Not configured'}`);
        } else {
            console.log('❌ ChatBot API is not available. Please start the Python backend.');
            if (window.chatBot.responseArea) {
                window.chatBot.showResponse('❌ Backend server not running. Please start the Python backend with: python Backend/Chatbot.py', 'error');
            }
        }
    });
});