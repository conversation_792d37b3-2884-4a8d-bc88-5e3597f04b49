// ChatBot Frontend JavaScript
// Handles user input and direct API communication

class ChatBot {
    constructor() {
        this.isLoading = false;
        this.apiKey = '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';
        this.apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
        this.model = 'deepseek/deepseek-chat-v3:free';
        this.chatHistory = [];
        this.sessionId = this.generateSessionId();
        this.initializeEventListeners();
        this.clearHistoryOnLoad();
    }

    generateSessionId() {
        return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    initializeEventListeners() {
        // Get DOM elements
        this.questionInput = document.getElementById('questionInput');
        this.sendButton = document.getElementById('sendButton');
        this.responseArea = document.getElementById('responseArea');
        this.clearButton = document.getElementById('clearButton');

        // Add event listeners
        if (this.sendButton) {
            this.sendButton.addEventListener('click', () => this.sendMessage());
        }

        if (this.questionInput) {
            this.questionInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }

        if (this.clearButton) {
            this.clearButton.addEventListener('click', () => this.clearHistory());
        }
    }

    clearHistoryOnLoad() {
        // Clear history when page loads (simulates refresh)
        this.chatHistory = [];
        this.saveHistoryToStorage();
        console.log('Chat history cleared on page load');
    }

    saveHistoryToStorage() {
        const historyData = {
            conversations: this.chatHistory,
            session_id: this.sessionId,
            created_at: new Date().toISOString()
        };
        localStorage.setItem('chatbot_history', JSON.stringify(historyData));
    }

    loadHistoryFromStorage() {
        try {
            const stored = localStorage.getItem('chatbot_history');
            if (stored) {
                const data = JSON.parse(stored);
                this.chatHistory = data.conversations || [];
                return data;
            }
        } catch (error) {
            console.error('Error loading history:', error);
        }
        return { conversations: [], session_id: this.sessionId, created_at: new Date().toISOString() };
    }

    async callOpenRouterAPI(userMessage) {
        try {
            // Prepare messages array with conversation history
            const messages = [];

            // Add recent conversation history (last 5 exchanges to avoid token limits)
            const recentHistory = this.chatHistory.slice(-5);
            for (const conv of recentHistory) {
                messages.push({ role: 'user', content: conv.user_message });
                messages.push({ role: 'assistant', content: conv.bot_response });
            }

            // Add current user message
            messages.push({ role: 'user', content: userMessage });

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': window.location.origin,
                    'X-Title': 'ChatBot Application'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: messages,
                    temperature: 0.7,
                    max_tokens: 1500,
                    stream: false
                })
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if (data.choices && data.choices.length > 0) {
                const botResponse = data.choices[0].message.content;

                // Save to history
                const conversationEntry = {
                    timestamp: new Date().toISOString(),
                    user_message: userMessage,
                    bot_response: botResponse,
                    message_id: this.generateMessageId()
                };

                this.chatHistory.push(conversationEntry);
                this.saveHistoryToStorage();

                return {
                    success: true,
                    user_message: userMessage,
                    response: botResponse,
                    session_id: this.sessionId,
                    timestamp: conversationEntry.timestamp,
                    usage: data.usage || {}
                };
            } else {
                throw new Error('No response from AI model');
            }

        } catch (error) {
            console.error('OpenRouter API Error:', error);
            return {
                success: false,
                response: `Error: ${error.message}`
            };
        }
    }

    generateMessageId() {
        return 'msg-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);
    }

    async sendMessage() {
        if (this.isLoading) return;

        const question = this.questionInput.value.trim();

        if (!question) {
            this.showResponse('❌ Please enter a question.', 'error');
            return;
        }

        // Show loading state
        this.setLoadingState(true);
        this.showResponse('🤔 Thinking...', 'loading');

        try {
            // Call OpenRouter API directly
            const data = await this.callOpenRouterAPI(question);

            if (data.success) {
                this.showResponse(data.response, 'success');
                this.questionInput.value = ''; // Clear input after successful response
            } else {
                this.showResponse(`❌ ${data.response}`, 'error');
            }

        } catch (error) {
            this.showResponse(`❌ Error: ${error.message}`, 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    async clearHistory() {
        try {
            this.chatHistory = [];
            this.saveHistoryToStorage();
            this.showResponse('🗑️ Chat history cleared!', 'info');
            this.questionInput.value = '';
            console.log('Chat history cleared manually');

        } catch (error) {
            this.showResponse(`❌ Error: ${error.message}`, 'error');
        }
    }

    showResponse(message, type = 'success') {
        if (!this.responseArea) return;

        // Create response element
        const responseElement = document.createElement('div');
        responseElement.className = `response ${type}`;

        // Add timestamp
        const timestamp = new Date().toLocaleTimeString();
        responseElement.innerHTML = `
            <div class="response-content">
                <div class="response-text">${message}</div>
                <div class="response-time">${timestamp}</div>
            </div>
        `;

        // Clear previous responses and add new one
        this.responseArea.innerHTML = '';
        this.responseArea.appendChild(responseElement);

        // Scroll to response
        responseElement.scrollIntoView({ behavior: 'smooth' });
    }

    setLoadingState(loading) {
        this.isLoading = loading;

        if (this.sendButton) {
            this.sendButton.disabled = loading;
            this.sendButton.textContent = loading ? 'Thinking...' : 'Send';
        }

        if (this.questionInput) {
            this.questionInput.disabled = loading;
        }
    }

    // Utility method to check API status
    checkApiStatus() {
        const status = {
            status: "ChatBot is ready",
            session_id: this.sessionId,
            model: this.model,
            api_key_configured: !!this.apiKey,
            direct_api: true
        };
        console.log('ChatBot Status:', status);
        return status;
    }
}

// Initialize ChatBot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatBot = new ChatBot();

    // Check API status on load
    const status = window.chatBot.checkApiStatus();
    console.log('✅ ChatBot initialized with direct API access');
    console.log(`📝 Session ID: ${status.session_id}`);
    console.log(`🎯 Model: ${status.model}`);
    console.log(`🔑 API Key: ${status.api_key_configured ? 'Configured' : 'Not configured'}`);
    console.log('🌐 Using direct OpenRouter API calls');
});