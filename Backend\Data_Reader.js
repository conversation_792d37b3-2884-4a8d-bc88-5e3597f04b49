// ChatBot Frontend JavaScript
// Handles user input and communication with Python backend

class ChatBot {
    constructor() {
        this.isLoading = false;
        this.initializeEventListeners();
        this.clearHistoryOnLoad();
    }

    initializeEventListeners() {
        // Get DOM elements
        this.questionInput = document.getElementById('questionInput');
        this.sendButton = document.getElementById('sendButton');
        this.responseArea = document.getElementById('responseArea');
        this.clearButton = document.getElementById('clearButton');

        // Add event listeners
        if (this.sendButton) {
            this.sendButton.addEventListener('click', () => this.sendMessage());
        }

        if (this.questionInput) {
            this.questionInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }

        if (this.clearButton) {
            this.clearButton.addEventListener('click', () => this.clearHistory());
        }
    }

    clearHistoryOnLoad() {
        // Clear history when page loads (simulates refresh)
        this.executePythonCommand('clear').catch(error => {
            console.log('Note: Could not clear history on load:', error.message);
        });
    }

    async executePythonCommand(command, message = '') {
        try {
            // Create a simple HTTP request to trigger Python execution
            // We'll write the input to a file and let Python read it
            const inputData = {
                command: command,
                message: message,
                timestamp: new Date().toISOString()
            };

            // Write input to a temporary file that Python can read
            await this.writeInputFile(inputData);

            // Give Python a moment to process
            await this.sleep(100);

            // Try to read the output file
            const result = await this.readOutputFile();

            return result;

        } catch (error) {
            console.error('Error executing Python command:', error);
            return {
                success: false,
                response: `Error: ${error.message}`
            };
        }
    }

    async writeInputFile(data) {
        // Since we can't write files directly from browser, we'll use localStorage
        // and create a simple polling mechanism
        localStorage.setItem('chatbot_input', JSON.stringify(data));
        localStorage.setItem('chatbot_input_timestamp', Date.now().toString());
    }

    async readOutputFile() {
        // For now, we'll simulate the Python response
        // In a real implementation, you'd need a file watcher or polling mechanism
        const input = JSON.parse(localStorage.getItem('chatbot_input') || '{}');

        if (input.command === 'clear') {
            localStorage.removeItem('chatbot_history');
            return {
                success: true,
                message: "Chat history cleared successfully"
            };
        } else if (input.command === 'status') {
            return {
                status: "ChatBot is ready",
                model: "deepseek/deepseek-chat-v3:free",
                api_key_configured: true
            };
        } else if (input.command === 'chat') {
            // This is where we'd normally call the Python script
            // For demonstration, we'll return a mock response
            return {
                success: true,
                user_message: input.message,
                response: "I'm a demo response. To get real AI responses, you need to run the Python backend server.",
                timestamp: new Date().toISOString()
            };
        }

        return {
            success: false,
            response: "Unknown command"
        };
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async sendMessage() {
        if (this.isLoading) return;

        const question = this.questionInput.value.trim();

        if (!question) {
            this.showResponse('❌ Please enter a question.', 'error');
            return;
        }

        // Show loading state
        this.setLoadingState(true);
        this.showResponse('🤔 Thinking...', 'loading');

        try {
            // Execute Python command
            const data = await this.executePythonCommand('chat', question);

            if (data.success) {
                this.showResponse(data.response, 'success');
                this.questionInput.value = ''; // Clear input after successful response
            } else {
                this.showResponse(`❌ Error: ${data.response}`, 'error');
            }

        } catch (error) {
            this.showResponse(`❌ Error: ${error.message}`, 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    async clearHistory() {
        try {
            const data = await this.executePythonCommand('clear');

            if (data.success) {
                this.showResponse('🗑️ Chat history cleared!', 'info');
                this.questionInput.value = '';
            } else {
                this.showResponse(`❌ Error clearing history: ${data.message}`, 'error');
            }

        } catch (error) {
            this.showResponse(`❌ Error: ${error.message}`, 'error');
        }
    }

    showResponse(message, type = 'success') {
        if (!this.responseArea) return;

        // Create response element
        const responseElement = document.createElement('div');
        responseElement.className = `response ${type}`;

        // Add timestamp
        const timestamp = new Date().toLocaleTimeString();
        responseElement.innerHTML = `
            <div class="response-content">
                <div class="response-text">${message}</div>
                <div class="response-time">${timestamp}</div>
            </div>
        `;

        // Clear previous responses and add new one
        this.responseArea.innerHTML = '';
        this.responseArea.appendChild(responseElement);

        // Scroll to response
        responseElement.scrollIntoView({ behavior: 'smooth' });
    }

    setLoadingState(loading) {
        this.isLoading = loading;

        if (this.sendButton) {
            this.sendButton.disabled = loading;
            this.sendButton.textContent = loading ? 'Thinking...' : 'Send';
        }

        if (this.questionInput) {
            this.questionInput.disabled = loading;
        }
    }

    // Utility method to check API status
    async checkApiStatus() {
        try {
            const data = await this.executePythonCommand('status');
            console.log('ChatBot Status:', data);
            return data;
        } catch (error) {
            console.error('ChatBot not available:', error);
            return null;
        }
    }
}

// Initialize ChatBot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatBot = new ChatBot();

    // Check API status on load
    window.chatBot.checkApiStatus().then(status => {
        if (status) {
            console.log('✅ ChatBot API is running');
            console.log(`📝 Session ID: ${status.session_id}`);
            console.log(`🎯 Model: ${status.model}`);
            console.log(`🔑 API Key: ${status.api_key_configured ? 'Configured' : 'Not configured'}`);
        } else {
            console.log('❌ ChatBot API is not available. Please start the Python backend.');
            if (window.chatBot.responseArea) {
                window.chatBot.showResponse('❌ Backend server not running. Please start the Python backend with: python Backend/Chatbot.py', 'error');
            }
        }
    });
});